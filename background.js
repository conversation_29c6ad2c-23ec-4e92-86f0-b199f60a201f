"use strict";
/*!
 *  decimal.js v10.5.0
 *  An arbitrary-precision Decimal type for JavaScript.
 *  https://github.com/MikeMcl/decimal.js
 *  Copyright (c) 2025 <PERSON> <<EMAIL>>
 *  MIT Licence
 */
var n,e,t=9e15,i=1e9,r="0123456789abcdef",s="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",o="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",u={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:t,crypto:!1},c=!0,a="[DecimalError] ",l=a+"Invalid argument: ",f=a+"Precision limit exceeded",d=a+"crypto unavailable",h="[object Decimal]",g=Math.floor,p=Math.pow,m=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,w=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,v=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,N=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,y=1e7,b=s.length-1,x=o.length-1,E={toStringTag:h};function M(n){var e,t,i,r=n.length-1,s="",o=n[0];if(r>0){for(s+=o,e=1;e<r;e++)(t=7-(i=n[e]+"").length)&&(s+=_(t)),s+=i;(t=7-(i=(o=n[e])+"").length)&&(s+=_(t))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function q(n,e,t){if(n!==~~n||n<e||n>t)throw Error(l+n)}function O(n,e,t,i){var r,s,o,u;for(s=n[0];s>=10;s/=10)--e;return--e<0?(e+=7,r=0):(r=Math.ceil((e+1)/7),e%=7),s=p(10,7-e),u=n[r]%s|0,null==i?e<3?(0==e?u=u/100|0:1==e&&(u=u/10|0),o=t<4&&99999==u||t>3&&49999==u||5e4==u||0==u):o=(t<4&&u+1==s||t>3&&u+1==s/2)&&(n[r+1]/s/100|0)==p(10,e-2)-1||(u==s/2||0==u)&&!(n[r+1]/s/100|0):e<4?(0==e?u=u/1e3|0:1==e?u=u/100|0:2==e&&(u=u/10|0),o=(i||t<4)&&9999==u||!i&&t>3&&4999==u):o=((i||t<4)&&u+1==s||!i&&t>3&&u+1==s/2)&&(n[r+1]/s/1e3|0)==p(10,e-3)-1,o}function S(n,e,t){for(var i,s,o=[0],u=0,c=n.length;u<c;){for(s=o.length;s--;)o[s]*=e;for(o[0]+=r.indexOf(n.charAt(u++)),i=0;i<o.length;i++)o[i]>t-1&&(void 0===o[i+1]&&(o[i+1]=0),o[i+1]+=o[i]/t|0,o[i]%=t)}return o.reverse()}E.absoluteValue=E.abs=function(){var n=new this.constructor(this);return n.s<0&&(n.s=1),A(n)},E.ceil=function(){return A(new this.constructor(this),this.e+1,2)},E.clampedTo=E.clamp=function(n,e){var t=this,i=t.constructor;if(n=new i(n),e=new i(e),!n.s||!e.s)return new i(NaN);if(n.gt(e))throw Error(l+e);return t.cmp(n)<0?n:t.cmp(e)>0?e:new i(t)},E.comparedTo=E.cmp=function(n){var e,t,i,r,s=this,o=s.d,u=(n=new s.constructor(n)).d,c=s.s,a=n.s;if(!o||!u)return c&&a?c!==a?c:o===u?0:!o^c<0?1:-1:NaN;if(!o[0]||!u[0])return o[0]?c:u[0]?-a:0;if(c!==a)return c;if(s.e!==n.e)return s.e>n.e^c<0?1:-1;for(e=0,t=(i=o.length)<(r=u.length)?i:r;e<t;++e)if(o[e]!==u[e])return o[e]>u[e]^c<0?1:-1;return i===r?0:i>r^c<0?1:-1},E.cosine=E.cos=function(){var n,t,i=this,r=i.constructor;return i.d?i.d[0]?(n=r.precision,t=r.rounding,r.precision=n+Math.max(i.e,i.sd())+7,r.rounding=1,i=function(n,e){var t,i,r;if(e.isZero())return e;i=e.d.length,i<32?r=(1/V(4,t=Math.ceil(i/3))).toString():(t=16,r="2.3283064365386962890625e-10");n.precision+=t,e=B(n,1,e.times(r),new n(1));for(var s=t;s--;){var o=e.times(e);e=o.times(o).minus(o).times(8).plus(1)}return n.precision-=t,e}(r,$(r,i)),r.precision=n,r.rounding=t,A(2==e||3==e?i.neg():i,n,t,!0)):new r(1):new r(NaN)},E.cubeRoot=E.cbrt=function(){var n,e,t,i,r,s,o,u,a,l,f=this,d=f.constructor;if(!f.isFinite()||f.isZero())return new d(f);for(c=!1,(s=f.s*p(f.s*f,1/3))&&Math.abs(s)!=1/0?i=new d(s.toString()):(t=M(f.d),(s=((n=f.e)-t.length+1)%3)&&(t+=1==s||-2==s?"0":"00"),s=p(t,1/3),n=g((n+1)/3)-(n%3==(n<0?-1:2)),(i=new d(t=s==1/0?"5e"+n:(t=s.toExponential()).slice(0,t.indexOf("e")+1)+n)).s=f.s),o=(n=d.precision)+3;;)if(l=(a=(u=i).times(u).times(u)).plus(f),i=T(l.plus(f).times(u),l.plus(a),o+2,1),M(u.d).slice(0,o)===(t=M(i.d)).slice(0,o)){if("9999"!=(t=t.slice(o-3,o+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(A(i,n+1,1),e=!i.times(i).times(i).eq(f));break}if(!r&&(A(u,n+1,0),u.times(u).times(u).eq(f))){i=u;break}o+=4,r=1}return c=!0,A(i,n,d.rounding,e)},E.decimalPlaces=E.dp=function(){var n,e=this.d,t=NaN;if(e){if(t=7*((n=e.length-1)-g(this.e/7)),n=e[n])for(;n%10==0;n/=10)t--;t<0&&(t=0)}return t},E.dividedBy=E.div=function(n){return T(this,new this.constructor(n))},E.dividedToIntegerBy=E.divToInt=function(n){var e=this.constructor;return A(T(this,new e(n),0,1,1),e.precision,e.rounding)},E.equals=E.eq=function(n){return 0===this.cmp(n)},E.floor=function(){return A(new this.constructor(this),this.e+1,3)},E.greaterThan=E.gt=function(n){return this.cmp(n)>0},E.greaterThanOrEqualTo=E.gte=function(n){var e=this.cmp(n);return 1==e||0===e},E.hyperbolicCosine=E.cosh=function(){var n,e,t,i,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;t=o.precision,i=o.rounding,o.precision=t+Math.max(s.e,s.sd())+4,o.rounding=1,(r=s.d.length)<32?e=(1/V(4,n=Math.ceil(r/3))).toString():(n=16,e="2.3283064365386962890625e-10"),s=B(o,1,s.times(e),new o(1),!0);for(var c,a=n,l=new o(8);a--;)c=s.times(s),s=u.minus(c.times(l.minus(c.times(l))));return A(s,o.precision=t,o.rounding=i,!0)},E.hyperbolicSine=E.sinh=function(){var n,e,t,i,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(e=s.precision,t=s.rounding,s.precision=e+Math.max(r.e,r.sd())+4,s.rounding=1,(i=r.d.length)<3)r=B(s,2,r,r,!0);else{n=(n=1.4*Math.sqrt(i))>16?16:0|n,r=B(s,2,r=r.times(1/V(5,n)),r,!0);for(var o,u=new s(5),c=new s(16),a=new s(20);n--;)o=r.times(r),r=r.times(u.plus(o.times(c.times(o).plus(a))))}return s.precision=e,s.rounding=t,A(r,e,t,!0)},E.hyperbolicTangent=E.tanh=function(){var n,e,t=this,i=t.constructor;return t.isFinite()?t.isZero()?new i(t):(n=i.precision,e=i.rounding,i.precision=n+7,i.rounding=1,T(t.sinh(),t.cosh(),i.precision=n,i.rounding=e)):new i(t.s)},E.inverseCosine=E.acos=function(){var n=this,e=n.constructor,t=n.abs().cmp(1),i=e.precision,r=e.rounding;return-1!==t?0===t?n.isNeg()?D(e,i,r):new e(0):new e(NaN):n.isZero()?D(e,i+4,r).times(.5):(e.precision=i+6,e.rounding=1,n=new e(1).minus(n).div(n.plus(1)).sqrt().atan(),e.precision=i,e.rounding=r,n.times(2))},E.inverseHyperbolicCosine=E.acosh=function(){var n,e,t=this,i=t.constructor;return t.lte(1)?new i(t.eq(1)?0:NaN):t.isFinite()?(n=i.precision,e=i.rounding,i.precision=n+Math.max(Math.abs(t.e),t.sd())+4,i.rounding=1,c=!1,t=t.times(t).minus(1).sqrt().plus(t),c=!0,i.precision=n,i.rounding=e,t.ln()):new i(t)},E.inverseHyperbolicSine=E.asinh=function(){var n,e,t=this,i=t.constructor;return!t.isFinite()||t.isZero()?new i(t):(n=i.precision,e=i.rounding,i.precision=n+2*Math.max(Math.abs(t.e),t.sd())+6,i.rounding=1,c=!1,t=t.times(t).plus(1).sqrt().plus(t),c=!0,i.precision=n,i.rounding=e,t.ln())},E.inverseHyperbolicTangent=E.atanh=function(){var n,e,t,i,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(n=s.precision,e=s.rounding,i=r.sd(),Math.max(i,n)<2*-r.e-1?A(new s(r),n,e,!0):(s.precision=t=i-r.e,r=T(r.plus(1),new s(1).minus(r),t+n,1),s.precision=n+4,s.rounding=1,r=r.ln(),s.precision=n,s.rounding=e,r.times(.5))):new s(NaN)},E.inverseSine=E.asin=function(){var n,e,t,i,r=this,s=r.constructor;return r.isZero()?new s(r):(e=r.abs().cmp(1),t=s.precision,i=s.rounding,-1!==e?0===e?((n=D(s,t+4,i).times(.5)).s=r.s,n):new s(NaN):(s.precision=t+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=t,s.rounding=i,r.times(2)))},E.inverseTangent=E.atan=function(){var n,e,t,i,r,s,o,u,a,l=this,f=l.constructor,d=f.precision,h=f.rounding;if(l.isFinite()){if(l.isZero())return new f(l);if(l.abs().eq(1)&&d+4<=x)return(o=D(f,d+4,h).times(.25)).s=l.s,o}else{if(!l.s)return new f(NaN);if(d+4<=x)return(o=D(f,d+4,h).times(.5)).s=l.s,o}for(f.precision=u=d+10,f.rounding=1,n=t=Math.min(28,u/7+2|0);n;--n)l=l.div(l.times(l).plus(1).sqrt().plus(1));for(c=!1,e=Math.ceil(u/7),i=1,a=l.times(l),o=new f(l),r=l;-1!==n;)if(r=r.times(a),s=o.minus(r.div(i+=2)),r=r.times(a),void 0!==(o=s.plus(r.div(i+=2))).d[e])for(n=e;o.d[n]===s.d[n]&&n--;);return t&&(o=o.times(2<<t-1)),c=!0,A(o,f.precision=d,f.rounding=h,!0)},E.isFinite=function(){return!!this.d},E.isInteger=E.isInt=function(){return!!this.d&&g(this.e/7)>this.d.length-2},E.isNaN=function(){return!this.s},E.isNegative=E.isNeg=function(){return this.s<0},E.isPositive=E.isPos=function(){return this.s>0},E.isZero=function(){return!!this.d&&0===this.d[0]},E.lessThan=E.lt=function(n){return this.cmp(n)<0},E.lessThanOrEqualTo=E.lte=function(n){return this.cmp(n)<1},E.logarithm=E.log=function(n){var e,t,i,r,s,o,u,a,l=this,f=l.constructor,d=f.precision,h=f.rounding;if(null==n)n=new f(10),e=!0;else{if(t=(n=new f(n)).d,n.s<0||!t||!t[0]||n.eq(1))return new f(NaN);e=n.eq(10)}if(t=l.d,l.s<0||!t||!t[0]||l.eq(1))return new f(t&&!t[0]?-1/0:1!=l.s?NaN:t?0:1/0);if(e)if(t.length>1)s=!0;else{for(r=t[0];r%10==0;)r/=10;s=1!==r}if(c=!1,o=C(l,u=d+5),i=e?Z(f,u+10):C(n,u),O((a=T(o,i,u,1)).d,r=d,h))do{if(o=C(l,u+=10),i=e?Z(f,u+10):C(n,u),a=T(o,i,u,1),!s){+M(a.d).slice(r+1,r+15)+1==1e14&&(a=A(a,d+1,0));break}}while(O(a.d,r+=10,h));return c=!0,A(a,d,h)},E.minus=E.sub=function(n){var e,t,i,r,s,o,u,a,l,f,d,h,p=this,m=p.constructor;if(n=new m(n),!p.d||!n.d)return p.s&&n.s?p.d?n.s=-n.s:n=new m(n.d||p.s!==n.s?p:NaN):n=new m(NaN),n;if(p.s!=n.s)return n.s=-n.s,p.plus(n);if(l=p.d,h=n.d,u=m.precision,a=m.rounding,!l[0]||!h[0]){if(h[0])n.s=-n.s;else{if(!l[0])return new m(3===a?-0:0);n=new m(p)}return c?A(n,u,a):n}if(t=g(n.e/7),f=g(p.e/7),l=l.slice(),s=f-t){for((d=s<0)?(e=l,s=-s,o=h.length):(e=h,t=f,o=l.length),s>(i=Math.max(Math.ceil(u/7),o)+2)&&(s=i,e.length=1),e.reverse(),i=s;i--;)e.push(0);e.reverse()}else{for((d=(i=l.length)<(o=h.length))&&(o=i),i=0;i<o;i++)if(l[i]!=h[i]){d=l[i]<h[i];break}s=0}for(d&&(e=l,l=h,h=e,n.s=-n.s),o=l.length,i=h.length-o;i>0;--i)l[o++]=0;for(i=h.length;i>s;){if(l[--i]<h[i]){for(r=i;r&&0===l[--r];)l[r]=y-1;--l[r],l[i]+=y}l[i]-=h[i]}for(;0===l[--o];)l.pop();for(;0===l[0];l.shift())--t;return l[0]?(n.d=l,n.e=I(l,t),c?A(n,u,a):n):new m(3===a?-0:0)},E.modulo=E.mod=function(n){var e,t=this,i=t.constructor;return n=new i(n),!t.d||!n.s||n.d&&!n.d[0]?new i(NaN):!n.d||t.d&&!t.d[0]?A(new i(t),i.precision,i.rounding):(c=!1,9==i.modulo?(e=T(t,n.abs(),0,3,1)).s*=n.s:e=T(t,n,0,i.modulo,1),e=e.times(n),c=!0,t.minus(e))},E.naturalExponential=E.exp=function(){return U(this)},E.naturalLogarithm=E.ln=function(){return C(this)},E.negated=E.neg=function(){var n=new this.constructor(this);return n.s=-n.s,A(n)},E.plus=E.add=function(n){var e,t,i,r,s,o,u,a,l,f,d=this,h=d.constructor;if(n=new h(n),!d.d||!n.d)return d.s&&n.s?d.d||(n=new h(n.d||d.s===n.s?d:NaN)):n=new h(NaN),n;if(d.s!=n.s)return n.s=-n.s,d.minus(n);if(l=d.d,f=n.d,u=h.precision,a=h.rounding,!l[0]||!f[0])return f[0]||(n=new h(d)),c?A(n,u,a):n;if(s=g(d.e/7),i=g(n.e/7),l=l.slice(),r=s-i){for(r<0?(t=l,r=-r,o=f.length):(t=f,i=s,o=l.length),r>(o=(s=Math.ceil(u/7))>o?s+1:o+1)&&(r=o,t.length=1),t.reverse();r--;)t.push(0);t.reverse()}for((o=l.length)-(r=f.length)<0&&(r=o,t=f,f=l,l=t),e=0;r;)e=(l[--r]=l[r]+f[r]+e)/y|0,l[r]%=y;for(e&&(l.unshift(e),++i),o=l.length;0==l[--o];)l.pop();return n.d=l,n.e=I(l,i),c?A(n,u,a):n},E.precision=E.sd=function(n){var e,t=this;if(void 0!==n&&n!==!!n&&1!==n&&0!==n)throw Error(l+n);return t.d?(e=L(t.d),n&&t.e+1>e&&(e=t.e+1)):e=NaN,e},E.round=function(){var n=this,e=n.constructor;return A(new e(n),n.e+1,e.rounding)},E.sine=E.sin=function(){var n,t,i=this,r=i.constructor;return i.isFinite()?i.isZero()?new r(i):(n=r.precision,t=r.rounding,r.precision=n+Math.max(i.e,i.sd())+7,r.rounding=1,i=function(n,e){var t,i=e.d.length;if(i<3)return e.isZero()?e:B(n,2,e,e);t=(t=1.4*Math.sqrt(i))>16?16:0|t,e=e.times(1/V(5,t)),e=B(n,2,e,e);for(var r,s=new n(5),o=new n(16),u=new n(20);t--;)r=e.times(e),e=e.times(s.plus(r.times(o.times(r).minus(u))));return e}(r,$(r,i)),r.precision=n,r.rounding=t,A(e>2?i.neg():i,n,t,!0)):new r(NaN)},E.squareRoot=E.sqrt=function(){var n,e,t,i,r,s,o=this,u=o.d,a=o.e,l=o.s,f=o.constructor;if(1!==l||!u||!u[0])return new f(!l||l<0&&(!u||u[0])?NaN:u?o:1/0);for(c=!1,0==(l=Math.sqrt(+o))||l==1/0?(((e=M(u)).length+a)%2==0&&(e+="0"),l=Math.sqrt(e),a=g((a+1)/2)-(a<0||a%2),i=new f(e=l==1/0?"5e"+a:(e=l.toExponential()).slice(0,e.indexOf("e")+1)+a)):i=new f(l.toString()),t=(a=f.precision)+3;;)if(i=(s=i).plus(T(o,s,t+2,1)).times(.5),M(s.d).slice(0,t)===(e=M(i.d)).slice(0,t)){if("9999"!=(e=e.slice(t-3,t+1))&&(r||"4999"!=e)){+e&&(+e.slice(1)||"5"!=e.charAt(0))||(A(i,a+1,1),n=!i.times(i).eq(o));break}if(!r&&(A(s,a+1,0),s.times(s).eq(o))){i=s;break}t+=4,r=1}return c=!0,A(i,a,f.rounding,n)},E.tangent=E.tan=function(){var n,t,i=this,r=i.constructor;return i.isFinite()?i.isZero()?new r(i):(n=r.precision,t=r.rounding,r.precision=n+10,r.rounding=1,(i=i.sin()).s=1,i=T(i,new r(1).minus(i.times(i)).sqrt(),n+10,0),r.precision=n,r.rounding=t,A(2==e||4==e?i.neg():i,n,t,!0)):new r(NaN)},E.times=E.mul=function(n){var e,t,i,r,s,o,u,a,l,f=this,d=f.constructor,h=f.d,p=(n=new d(n)).d;if(n.s*=f.s,!(h&&h[0]&&p&&p[0]))return new d(!n.s||h&&!h[0]&&!p||p&&!p[0]&&!h?NaN:h&&p?0*n.s:n.s/0);for(t=g(f.e/7)+g(n.e/7),(a=h.length)<(l=p.length)&&(s=h,h=p,p=s,o=a,a=l,l=o),s=[],i=o=a+l;i--;)s.push(0);for(i=l;--i>=0;){for(e=0,r=a+i;r>i;)u=s[r]+p[i]*h[r-i-1]+e,s[r--]=u%y|0,e=u/y|0;s[r]=(s[r]+e)%y|0}for(;!s[--o];)s.pop();return e?++t:s.shift(),n.d=s,n.e=I(s,t),c?A(n,d.precision,d.rounding):n},E.toBinary=function(n,e){return W(this,2,n,e)},E.toDecimalPlaces=E.toDP=function(n,e){var t=this,r=t.constructor;return t=new r(t),void 0===n?t:(q(n,0,i),void 0===e?e=r.rounding:q(e,0,8),A(t,n+t.e+1,e))},E.toExponential=function(n,e){var t,r=this,s=r.constructor;return void 0===n?t=F(r,!0):(q(n,0,i),void 0===e?e=s.rounding:q(e,0,8),t=F(r=A(new s(r),n+1,e),!0,n+1)),r.isNeg()&&!r.isZero()?"-"+t:t},E.toFixed=function(n,e){var t,r,s=this,o=s.constructor;return void 0===n?t=F(s):(q(n,0,i),void 0===e?e=o.rounding:q(e,0,8),t=F(r=A(new o(s),n+s.e+1,e),!1,n+r.e+1)),s.isNeg()&&!s.isZero()?"-"+t:t},E.toFraction=function(n){var e,t,i,r,s,o,u,a,f,d,h,g,m=this,w=m.d,v=m.constructor;if(!w)return new v(m);if(f=t=new v(1),i=a=new v(0),o=(s=(e=new v(i)).e=L(w)-m.e-1)%7,e.d[0]=p(10,o<0?7+o:o),null==n)n=s>0?e:f;else{if(!(u=new v(n)).isInt()||u.lt(f))throw Error(l+u);n=u.gt(e)?s>0?e:f:u}for(c=!1,u=new v(M(w)),d=v.precision,v.precision=s=7*w.length*2;h=T(u,e,0,1,1),1!=(r=t.plus(h.times(i))).cmp(n);)t=i,i=r,r=f,f=a.plus(h.times(r)),a=r,r=e,e=u.minus(h.times(r)),u=r;return r=T(n.minus(t),i,0,1,1),a=a.plus(r.times(f)),t=t.plus(r.times(i)),a.s=f.s=m.s,g=T(f,i,s,1).minus(m).abs().cmp(T(a,t,s,1).minus(m).abs())<1?[f,i]:[a,t],v.precision=d,c=!0,g},E.toHexadecimal=E.toHex=function(n,e){return W(this,16,n,e)},E.toNearest=function(n,e){var t=this,i=t.constructor;if(t=new i(t),null==n){if(!t.d)return t;n=new i(1),e=i.rounding}else{if(n=new i(n),void 0===e?e=i.rounding:q(e,0,8),!t.d)return n.s?t:n;if(!n.d)return n.s&&(n.s=t.s),n}return n.d[0]?(c=!1,t=T(t,n,0,e,1).times(n),c=!0,A(t)):(n.s=t.s,t=n),t},E.toNumber=function(){return+this},E.toOctal=function(n,e){return W(this,8,n,e)},E.toPower=E.pow=function(n){var e,t,i,r,s,o,u=this,a=u.constructor,l=+(n=new a(n));if(!(u.d&&n.d&&u.d[0]&&n.d[0]))return new a(p(+u,l));if((u=new a(u)).eq(1))return u;if(i=a.precision,s=a.rounding,n.eq(1))return A(u,i,s);if((e=g(n.e/7))>=n.d.length-1&&(t=l<0?-l:l)<=9007199254740991)return r=k(a,u,t,i),n.s<0?new a(1).div(r):A(r,i,s);if((o=u.s)<0){if(e<n.d.length-1)return new a(NaN);if(1&n.d[e]||(o=1),0==u.e&&1==u.d[0]&&1==u.d.length)return u.s=o,u}return(e=0!=(t=p(+u,l))&&isFinite(t)?new a(t+"").e:g(l*(Math.log("0."+M(u.d))/Math.LN10+u.e+1)))>a.maxE+1||e<a.minE-1?new a(e>0?o/0:0):(c=!1,a.rounding=u.s=1,t=Math.min(12,(e+"").length),(r=U(n.times(C(u,i+t)),i)).d&&O((r=A(r,i+5,1)).d,i,s)&&(e=i+10,+M((r=A(U(n.times(C(u,e+t)),e),e+5,1)).d).slice(i+1,i+15)+1==1e14&&(r=A(r,i+1,0))),r.s=o,c=!0,a.rounding=s,A(r,i,s))},E.toPrecision=function(n,e){var t,r=this,s=r.constructor;return void 0===n?t=F(r,r.e<=s.toExpNeg||r.e>=s.toExpPos):(q(n,1,i),void 0===e?e=s.rounding:q(e,0,8),t=F(r=A(new s(r),n,e),n<=r.e||r.e<=s.toExpNeg,n)),r.isNeg()&&!r.isZero()?"-"+t:t},E.toSignificantDigits=E.toSD=function(n,e){var t=this.constructor;return void 0===n?(n=t.precision,e=t.rounding):(q(n,1,i),void 0===e?e=t.rounding:q(e,0,8)),A(new t(this),n,e)},E.toString=function(){var n=this,e=n.constructor,t=F(n,n.e<=e.toExpNeg||n.e>=e.toExpPos);return n.isNeg()&&!n.isZero()?"-"+t:t},E.truncated=E.trunc=function(){return A(new this.constructor(this),this.e+1,1)},E.valueOf=E.toJSON=function(){var n=this,e=n.constructor,t=F(n,n.e<=e.toExpNeg||n.e>=e.toExpPos);return n.isNeg()?"-"+t:t};var T=function(){function e(n,e,t){var i,r=0,s=n.length;for(n=n.slice();s--;)i=n[s]*e+r,n[s]=i%t|0,r=i/t|0;return r&&n.unshift(r),n}function t(n,e,t,i){var r,s;if(t!=i)s=t>i?1:-1;else for(r=s=0;r<t;r++)if(n[r]!=e[r]){s=n[r]>e[r]?1:-1;break}return s}function i(n,e,t,i){for(var r=0;t--;)n[t]-=r,r=n[t]<e[t]?1:0,n[t]=r*i+n[t]-e[t];for(;!n[0]&&n.length>1;)n.shift()}return function(r,s,o,u,c,a){var l,f,d,h,p,m,w,v,N,b,x,E,M,q,O,S,T,F,I,Z,D=r.constructor,L=r.s==s.s?1:-1,_=r.d,k=s.d;if(!(_&&_[0]&&k&&k[0]))return new D(r.s&&s.s&&(_?!k||_[0]!=k[0]:k)?_&&0==_[0]||!k?0*L:L/0:NaN);for(a?(p=1,f=r.e-s.e):(a=y,p=7,f=g(r.e/p)-g(s.e/p)),I=k.length,T=_.length,b=(N=new D(L)).d=[],d=0;k[d]==(_[d]||0);d++);if(k[d]>(_[d]||0)&&f--,null==o?(q=o=D.precision,u=D.rounding):q=c?o+(r.e-s.e)+1:o,q<0)b.push(1),m=!0;else{if(q=q/p+2|0,d=0,1==I){for(h=0,k=k[0],q++;(d<T||h)&&q--;d++)O=h*a+(_[d]||0),b[d]=O/k|0,h=O%k|0;m=h||d<T}else{for((h=a/(k[0]+1)|0)>1&&(k=e(k,h,a),_=e(_,h,a),I=k.length,T=_.length),S=I,E=(x=_.slice(0,I)).length;E<I;)x[E++]=0;(Z=k.slice()).unshift(0),F=k[0],k[1]>=a/2&&++F;do{h=0,(l=t(k,x,I,E))<0?(M=x[0],I!=E&&(M=M*a+(x[1]||0)),(h=M/F|0)>1?(h>=a&&(h=a-1),1==(l=t(w=e(k,h,a),x,v=w.length,E=x.length))&&(h--,i(w,I<v?Z:k,v,a))):(0==h&&(l=h=1),w=k.slice()),(v=w.length)<E&&w.unshift(0),i(x,w,E,a),-1==l&&(l=t(k,x,I,E=x.length))<1&&(h++,i(x,I<E?Z:k,E,a)),E=x.length):0===l&&(h++,x=[0]),b[d++]=h,l&&x[0]?x[E++]=_[S]||0:(x=[_[S]],E=1)}while((S++<T||void 0!==x[0])&&q--);m=void 0!==x[0]}b[0]||b.shift()}if(1==p)N.e=f,n=m;else{for(d=1,h=b[0];h>=10;h/=10)d++;N.e=d+f*p-1,A(N,c?o+N.e+1:o,u,m)}return N}}();function A(n,e,t,i){var r,s,o,u,a,l,f,d,h,g=n.constructor;n:if(null!=e){if(!(d=n.d))return n;for(r=1,u=d[0];u>=10;u/=10)r++;if((s=e-r)<0)s+=7,o=e,a=(f=d[h=0])/p(10,r-o-1)%10|0;else if((h=Math.ceil((s+1)/7))>=(u=d.length)){if(!i)break n;for(;u++<=h;)d.push(0);f=a=0,r=1,o=(s%=7)-7+1}else{for(f=u=d[h],r=1;u>=10;u/=10)r++;a=(o=(s%=7)-7+r)<0?0:f/p(10,r-o-1)%10|0}if(i=i||e<0||void 0!==d[h+1]||(o<0?f:f%p(10,r-o-1)),l=t<4?(a||i)&&(0==t||t==(n.s<0?3:2)):a>5||5==a&&(4==t||i||6==t&&(s>0?o>0?f/p(10,r-o):0:d[h-1])%10&1||t==(n.s<0?8:7)),e<1||!d[0])return d.length=0,l?(e-=n.e+1,d[0]=p(10,(7-e%7)%7),n.e=-e||0):d[0]=n.e=0,n;if(0==s?(d.length=h,u=1,h--):(d.length=h+1,u=p(10,7-s),d[h]=o>0?(f/p(10,r-o)%p(10,o)|0)*u:0),l)for(;;){if(0==h){for(s=1,o=d[0];o>=10;o/=10)s++;for(o=d[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(n.e++,d[0]==y&&(d[0]=1));break}if(d[h]+=u,d[h]!=y)break;d[h--]=0,u=1}for(s=d.length;0===d[--s];)d.pop()}return c&&(n.e>g.maxE?(n.d=null,n.e=NaN):n.e<g.minE&&(n.e=0,n.d=[0])),n}function F(n,e,t){if(!n.isFinite())return H(n);var i,r=n.e,s=M(n.d),o=s.length;return e?(t&&(i=t-o)>0?s=s.charAt(0)+"."+s.slice(1)+_(i):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(n.e<0?"e":"e+")+n.e):r<0?(s="0."+_(-r-1)+s,t&&(i=t-o)>0&&(s+=_(i))):r>=o?(s+=_(r+1-o),t&&(i=t-r-1)>0&&(s=s+"."+_(i))):((i=r+1)<o&&(s=s.slice(0,i)+"."+s.slice(i)),t&&(i=t-o)>0&&(r+1===o&&(s+="."),s+=_(i))),s}function I(n,e){var t=n[0];for(e*=7;t>=10;t/=10)e++;return e}function Z(n,e,t){if(e>b)throw c=!0,t&&(n.precision=t),Error(f);return A(new n(s),e,1,!0)}function D(n,e,t){if(e>x)throw Error(f);return A(new n(o),e,t,!0)}function L(n){var e=n.length-1,t=7*e+1;if(e=n[e]){for(;e%10==0;e/=10)t--;for(e=n[0];e>=10;e/=10)t++}return t}function _(n){for(var e="";n--;)e+="0";return e}function k(n,e,t,i){var r,s=new n(1),o=Math.ceil(i/7+4);for(c=!1;;){if(t%2&&z((s=s.times(e)).d,o)&&(r=!0),0===(t=g(t/2))){t=s.d.length-1,r&&0===s.d[t]&&++s.d[t];break}z((e=e.times(e)).d,o)}return c=!0,s}function P(n){return 1&n.d[n.d.length-1]}function R(n,e,t){for(var i,r,s=new n(e[0]),o=0;++o<e.length;){if(!(r=new n(e[o])).s){s=r;break}((i=s.cmp(r))===t||0===i&&s.s===t)&&(s=r)}return s}function U(n,e){var t,i,r,s,o,u,a,l=0,f=0,d=0,h=n.constructor,g=h.rounding,m=h.precision;if(!n.d||!n.d[0]||n.e>17)return new h(n.d?n.d[0]?n.s<0?0:1/0:1:n.s?n.s<0?0:n:NaN);for(null==e?(c=!1,a=m):a=e,u=new h(.03125);n.e>-2;)n=n.times(u),d+=5;for(a+=i=Math.log(p(2,d))/Math.LN10*2+5|0,t=s=o=new h(1),h.precision=a;;){if(s=A(s.times(n),a,1),t=t.times(++f),M((u=o.plus(T(s,t,a,1))).d).slice(0,a)===M(o.d).slice(0,a)){for(r=d;r--;)o=A(o.times(o),a,1);if(null!=e)return h.precision=m,o;if(!(l<3&&O(o.d,a-i,g,l)))return A(o,h.precision=m,g,c=!0);h.precision=a+=10,t=s=u=new h(1),f=0,l++}o=u}}function C(n,e){var t,i,r,s,o,u,a,l,f,d,h,g=1,p=n,m=p.d,w=p.constructor,v=w.rounding,N=w.precision;if(p.s<0||!m||!m[0]||!p.e&&1==m[0]&&1==m.length)return new w(m&&!m[0]?-1/0:1!=p.s?NaN:m?0:p);if(null==e?(c=!1,f=N):f=e,w.precision=f+=10,i=(t=M(m)).charAt(0),!(Math.abs(s=p.e)<15e14))return l=Z(w,f+2,N).times(s+""),p=C(new w(i+"."+t.slice(1)),f-10).plus(l),w.precision=N,null==e?A(p,N,v,c=!0):p;for(;i<7&&1!=i||1==i&&t.charAt(1)>3;)i=(t=M((p=p.times(n)).d)).charAt(0),g++;for(s=p.e,i>1?(p=new w("0."+t),s++):p=new w(i+"."+t.slice(1)),d=p,a=o=p=T(p.minus(1),p.plus(1),f,1),h=A(p.times(p),f,1),r=3;;){if(o=A(o.times(h),f,1),M((l=a.plus(T(o,new w(r),f,1))).d).slice(0,f)===M(a.d).slice(0,f)){if(a=a.times(2),0!==s&&(a=a.plus(Z(w,f+2,N).times(s+""))),a=T(a,new w(g),f,1),null!=e)return w.precision=N,a;if(!O(a.d,f-10,v,u))return A(a,w.precision=N,v,c=!0);w.precision=f+=10,l=o=p=T(d.minus(1),d.plus(1),f,1),h=A(p.times(p),f,1),r=u=1}a=l,r+=2}}function H(n){return String(n.s*n.s/0)}function j(n,e){var t,i,r;for((t=e.indexOf("."))>-1&&(e=e.replace(".","")),(i=e.search(/e/i))>0?(t<0&&(t=i),t+=+e.slice(i+1),e=e.substring(0,i)):t<0&&(t=e.length),i=0;48===e.charCodeAt(i);i++);for(r=e.length;48===e.charCodeAt(r-1);--r);if(e=e.slice(i,r)){if(r-=i,n.e=t=t-i-1,n.d=[],i=(t+1)%7,t<0&&(i+=7),i<r){for(i&&n.d.push(+e.slice(0,i)),r-=7;i<r;)n.d.push(+e.slice(i,i+=7));i=7-(e=e.slice(i)).length}else i-=r;for(;i--;)e+="0";n.d.push(+e),c&&(n.e>n.constructor.maxE?(n.d=null,n.e=NaN):n.e<n.constructor.minE&&(n.e=0,n.d=[0]))}else n.e=0,n.d=[0];return n}function B(n,e,t,i,r){var s,o,u,a,l=n.precision,f=Math.ceil(l/7);for(c=!1,a=t.times(t),u=new n(i);;){if(o=T(u.times(a),new n(e++*e++),l,1),u=r?i.plus(o):i.minus(o),i=T(o.times(a),new n(e++*e++),l,1),void 0!==(o=u.plus(i)).d[f]){for(s=f;o.d[s]===u.d[s]&&s--;);if(-1==s)break}s=u,u=i,i=o,o=s}return c=!0,o.d.length=f+1,o}function V(n,e){for(var t=n;--e;)t*=n;return t}function $(n,t){var i,r=t.s<0,s=D(n,n.precision,1),o=s.times(.5);if((t=t.abs()).lte(o))return e=r?4:1,t;if((i=t.divToInt(s)).isZero())e=r?3:2;else{if((t=t.minus(i.times(s))).lte(o))return e=P(i)?r?2:3:r?4:1,t;e=P(i)?r?1:4:r?3:2}return t.minus(s).abs()}function W(e,t,s,o){var u,c,a,l,f,d,h,g,p,m=e.constructor,w=void 0!==s;if(w?(q(s,1,i),void 0===o?o=m.rounding:q(o,0,8)):(s=m.precision,o=m.rounding),e.isFinite()){for(w?(u=2,16==t?s=4*s-3:8==t&&(s=3*s-2)):u=t,(a=(h=F(e)).indexOf("."))>=0&&(h=h.replace(".",""),(p=new m(1)).e=h.length-a,p.d=S(F(p),10,u),p.e=p.d.length),c=f=(g=S(h,10,u)).length;0==g[--f];)g.pop();if(g[0]){if(a<0?c--:((e=new m(e)).d=g,e.e=c,g=(e=T(e,p,s,o,0,u)).d,c=e.e,d=n),a=g[s],l=u/2,d=d||void 0!==g[s+1],d=o<4?(void 0!==a||d)&&(0===o||o===(e.s<0?3:2)):a>l||a===l&&(4===o||d||6===o&&1&g[s-1]||o===(e.s<0?8:7)),g.length=s,d)for(;++g[--s]>u-1;)g[s]=0,s||(++c,g.unshift(1));for(f=g.length;!g[f-1];--f);for(a=0,h="";a<f;a++)h+=r.charAt(g[a]);if(w){if(f>1)if(16==t||8==t){for(a=16==t?4:3,--f;f%a;f++)h+="0";for(f=(g=S(h,u,t)).length;!g[f-1];--f);for(a=1,h="1.";a<f;a++)h+=r.charAt(g[a])}else h=h.charAt(0)+"."+h.slice(1);h=h+(c<0?"p":"p+")+c}else if(c<0){for(;++c;)h="0"+h;h="0."+h}else if(++c>f)for(c-=f;c--;)h+="0";else c<f&&(h=h.slice(0,c)+"."+h.slice(c))}else h=w?"0p+0":"0";h=(16==t?"0x":2==t?"0b":8==t?"0o":"")+h}else h=H(e);return e.s<0?"-"+h:h}function z(n,e){if(n.length>e)return n.length=e,!0}function J(n){return new this(n).abs()}function G(n){return new this(n).acos()}function X(n){return new this(n).acosh()}function K(n,e){return new this(n).plus(e)}function Q(n){return new this(n).asin()}function Y(n){return new this(n).asinh()}function nn(n){return new this(n).atan()}function en(n){return new this(n).atanh()}function tn(n,e){n=new this(n),e=new this(e);var t,i=this.precision,r=this.rounding,s=i+4;return n.s&&e.s?n.d||e.d?!e.d||n.isZero()?(t=e.s<0?D(this,i,r):new this(0)).s=n.s:!n.d||e.isZero()?(t=D(this,s,1).times(.5)).s=n.s:e.s<0?(this.precision=s,this.rounding=1,t=this.atan(T(n,e,s,1)),e=D(this,s,1),this.precision=i,this.rounding=r,t=n.s<0?t.minus(e):t.plus(e)):t=this.atan(T(n,e,s,1)):(t=D(this,s,1).times(e.s>0?.25:.75)).s=n.s:t=new this(NaN),t}function rn(n){return new this(n).cbrt()}function sn(n){return A(n=new this(n),n.e+1,2)}function on(n,e,t){return new this(n).clamp(e,t)}function un(n){if(!n||"object"!=typeof n)throw Error(a+"Object expected");var e,r,s,o=!0===n.defaults,c=["precision",1,i,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,t,"maxE",0,t,"minE",-9e15,0,"modulo",0,9];for(e=0;e<c.length;e+=3)if(r=c[e],o&&(this[r]=u[r]),void 0!==(s=n[r])){if(!(g(s)===s&&s>=c[e+1]&&s<=c[e+2]))throw Error(l+r+": "+s);this[r]=s}if(r="crypto",o&&(this[r]=u[r]),void 0!==(s=n[r])){if(!0!==s&&!1!==s&&0!==s&&1!==s)throw Error(l+r+": "+s);if(s){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(d);this[r]=!0}else this[r]=!1}return this}function cn(n){return new this(n).cos()}function an(n){return new this(n).cosh()}function ln(n,e){return new this(n).div(e)}function fn(n){return new this(n).exp()}function dn(n){return A(n=new this(n),n.e+1,3)}function hn(){var n,e,t=new this(0);for(c=!1,n=0;n<arguments.length;)if((e=new this(arguments[n++])).d)t.d&&(t=t.plus(e.times(e)));else{if(e.s)return c=!0,new this(1/0);t=e}return c=!0,t.sqrt()}function gn(n){return n instanceof _n||n&&n.toStringTag===h||!1}function pn(n){return new this(n).ln()}function mn(n,e){return new this(n).log(e)}function wn(n){return new this(n).log(2)}function vn(n){return new this(n).log(10)}function Nn(){return R(this,arguments,-1)}function yn(){return R(this,arguments,1)}function bn(n,e){return new this(n).mod(e)}function xn(n,e){return new this(n).mul(e)}function En(n,e){return new this(n).pow(e)}function Mn(n){var e,t,r,s,o=0,u=new this(1),c=[];if(void 0===n?n=this.precision:q(n,1,i),r=Math.ceil(n/7),this.crypto)if(crypto.getRandomValues)for(e=crypto.getRandomValues(new Uint32Array(r));o<r;)(s=e[o])>=429e7?e[o]=crypto.getRandomValues(new Uint32Array(1))[0]:c[o++]=s%1e7;else{if(!crypto.randomBytes)throw Error(d);for(e=crypto.randomBytes(r*=4);o<r;)(s=e[o]+(e[o+1]<<8)+(e[o+2]<<16)+((127&e[o+3])<<24))>=214e7?crypto.randomBytes(4).copy(e,o):(c.push(s%1e7),o+=4);o=r/4}else for(;o<r;)c[o++]=1e7*Math.random()|0;for(n%=7,(r=c[--o])&&n&&(s=p(10,7-n),c[o]=(r/s|0)*s);0===c[o];o--)c.pop();if(o<0)t=0,c=[0];else{for(t=-1;0===c[0];t-=7)c.shift();for(r=1,s=c[0];s>=10;s/=10)r++;r<7&&(t-=7-r)}return u.e=t,u.d=c,u}function qn(n){return A(n=new this(n),n.e+1,this.rounding)}function On(n){return(n=new this(n)).d?n.d[0]?n.s:0*n.s:n.s||NaN}function Sn(n){return new this(n).sin()}function Tn(n){return new this(n).sinh()}function An(n){return new this(n).sqrt()}function Fn(n,e){return new this(n).sub(e)}function In(){var n=0,e=arguments,t=new this(e[n]);for(c=!1;t.s&&++n<e.length;)t=t.plus(e[n]);return c=!0,A(t,this.precision,this.rounding)}function Zn(n){return new this(n).tan()}function Dn(n){return new this(n).tanh()}function Ln(n){return A(n=new this(n),n.e+1,1)}E[Symbol.for("nodejs.util.inspect.custom")]=E.toString,E[Symbol.toStringTag]="Decimal";var _n=E.constructor=function n(e){var t,i,r;function s(n){var e,t,i,r=this;if(!(r instanceof s))return new s(n);if(r.constructor=s,gn(n))return r.s=n.s,void(c?!n.d||n.e>s.maxE?(r.e=NaN,r.d=null):n.e<s.minE?(r.e=0,r.d=[0]):(r.e=n.e,r.d=n.d.slice()):(r.e=n.e,r.d=n.d?n.d.slice():n.d));if("number"===(i=typeof n)){if(0===n)return r.s=1/n<0?-1:1,r.e=0,void(r.d=[0]);if(n<0?(n=-n,r.s=-1):r.s=1,n===~~n&&n<1e7){for(e=0,t=n;t>=10;t/=10)e++;return void(c?e>s.maxE?(r.e=NaN,r.d=null):e<s.minE?(r.e=0,r.d=[0]):(r.e=e,r.d=[n]):(r.e=e,r.d=[n]))}return 0*n!=0?(n||(r.s=NaN),r.e=NaN,void(r.d=null)):j(r,n.toString())}if("string"===i)return 45===(t=n.charCodeAt(0))?(n=n.slice(1),r.s=-1):(43===t&&(n=n.slice(1)),r.s=1),N.test(n)?j(r,n):function(n,e){var t,i,r,s,o,u,a,f,d;if(e.indexOf("_")>-1){if(e=e.replace(/(\d)_(?=\d)/g,"$1"),N.test(e))return j(n,e)}else if("Infinity"===e||"NaN"===e)return+e||(n.s=NaN),n.e=NaN,n.d=null,n;if(w.test(e))t=16,e=e.toLowerCase();else if(m.test(e))t=2;else{if(!v.test(e))throw Error(l+e);t=8}for((s=e.search(/p/i))>0?(a=+e.slice(s+1),e=e.substring(2,s)):e=e.slice(2),o=(s=e.indexOf("."))>=0,i=n.constructor,o&&(s=(u=(e=e.replace(".","")).length)-s,r=k(i,new i(t),s,2*s)),s=d=(f=S(e,t,y)).length-1;0===f[s];--s)f.pop();return s<0?new i(0*n.s):(n.e=I(f,d),n.d=f,c=!1,o&&(n=T(n,r,4*u)),a&&(n=n.times(Math.abs(a)<54?p(2,a):_n.pow(2,a))),c=!0,n)}(r,n);if("bigint"===i)return n<0?(n=-n,r.s=-1):r.s=1,j(r,n.toString());throw Error(l+n)}if(s.prototype=E,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=un,s.clone=n,s.isDecimal=gn,s.abs=J,s.acos=G,s.acosh=X,s.add=K,s.asin=Q,s.asinh=Y,s.atan=nn,s.atanh=en,s.atan2=tn,s.cbrt=rn,s.ceil=sn,s.clamp=on,s.cos=cn,s.cosh=an,s.div=ln,s.exp=fn,s.floor=dn,s.hypot=hn,s.ln=pn,s.log=mn,s.log10=vn,s.log2=wn,s.max=Nn,s.min=yn,s.mod=bn,s.mul=xn,s.pow=En,s.random=Mn,s.round=qn,s.sign=On,s.sin=Sn,s.sinh=Tn,s.sqrt=An,s.sub=Fn,s.sum=In,s.tan=Zn,s.tanh=Dn,s.trunc=Ln,void 0===e&&(e={}),e&&!0!==e.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<r.length;)e.hasOwnProperty(i=r[t++])||(e[i]=this[i]);return s.config(e),s}(u);s=new _n(s),o=new _n(o);const kn=n=>{const e=JSON.parse('[\n    "axiom.trade/meme/",\n    "gmgn.ai/sol/token/",\n    "photon-sol.tinyastro.io/en/lp/",\n    "photon-sol.tinyastro.io/zh/lp/",\n    "xxyy.io/sol/",\n    "www.xxyy.io/sol/"\n]')||[];if("string"!=typeof n)return null;if(!e.some((e=>n.includes(e))))return null;const t=n.match(/[a-zA-Z0-9]{32,44}/g);return t&&0!==t.length?t[0]:null},Pn=[{label:"Axiom",url:"https://axiom.trade/",host:"axiom.trade",popup:!0},{label:"Photon",url:"https://photon-sol.tinyastro.io/",host:"photon-sol.tinyastro.io",popup:!0},{label:"Gmgn",url:"https://gmgn.ai/",host:"gmgn.ai",popup:!0},{label:"Xxyy",url:"https://www.xxyy.io/",host:"www.xxyy.io",popup:!1}],Rn=["axiom.trade","gmgn.ai","photon-sol.tinyastro.io","xxyy.io","www.xxyy.io"],{send_msg:Un}={send_msg:async(n,e)=>{try{const[t]=await chrome.tabs.query({active:!0,currentWindow:!0});await chrome.tabs.sendMessage(t.id,{type:n,message:e})}catch(t){}}};chrome.webNavigation.onBeforeNavigate.addListener((n=>{0===n.frameId&&(console.log("即将导航到:",null==n?void 0:n.url),Un("send_url",null==n?void 0:n.url))}));const Cn=async({message:n,messageType:e,active:t=!1})=>{try{chrome.tabs.query({},(t=>{console.log("存在的tabs:",t),t.forEach((async t=>{if(console.log(t),chrome.runtime.lastError)return void console.log("Tab does not exist:",chrome.runtime.lastError.message);const i=kn(null==t?void 0:t.url);if(Pn.some((n=>{var e;return null==(e=null==t?void 0:t.url)?void 0:e.includes(n.host)}))){"install"==e&&(await chrome.scripting.executeScript({target:{tabId:t.id},files:["content.js"]}),await chrome.scripting.insertCSS({target:{tabId:t.id},files:["content.css"]}));const r={...n,messageType:e,url:t.url,tokenId:i,messageTime:Date.now()};chrome.tabs.sendMessage(t.id,r,{frameId:0})}}))}))}catch(i){console.log("Error sending message to tabs:",i.message)}};function Hn(){chrome.tabs.query({},(n=>{n.forEach((n=>{var e;n.url&&(e=n.url,Rn.some((n=>(console.log("isAllowed====>",n,e),"string"==typeof e&&e.indexOf(n)>-1))))&&chrome.tabs.reload(n.id)}))}))}chrome.webNavigation.onHistoryStateUpdated.addListener((n=>{console.log("历史状态更新:",n.url,n.tabId),console.log("检测到新的路由变化:",n.url);const e=kn(n.url);chrome.tabs.sendMessage(n.tabId,{message:{details:n,token:e},messageType:"historyStateUpdated"},{frameId:0})})),chrome.runtime.onInstalled.addListener((async n=>{console.log("Extension installed/updated:",n),"install"===n.reason?(console.log("处理全新安装或重新安装"),Cn({message:n,messageType:"install"})):"update"===n.reason&&(console.log("处理更新或覆盖安装"),Hn(),Cn({message:{details:n},messageType:"update"}))})),chrome.tabs.onUpdated.addListener((async(n,e,t)=>{if("complete"===e.status&&(null==t?void 0:t.url)){if(Rn.some((n=>(console.log("isAllowed====>",n,null==t?void 0:t.url),"string"==typeof(null==t?void 0:t.url)&&t.url.indexOf(n)>-1)))){console.log("白名单内 准备注入");try{setTimeout((async()=>{await chrome.scripting.executeScript({target:{tabId:n},files:["content.js"]}),await chrome.scripting.insertCSS({target:{tabId:n},files:["content.css"]}),console.log(`Injected into ${null==t?void 0:t.url}`)}),1500)}catch(i){console.log(`Failed to inject into ${null==t?void 0:t.url}:`,i.message)}}}})),chrome.storage.onChanged.addListener((async(n,e)=>{console.log("background script storage 变化",n,e),Cn({message:n,messageType:"storage_change"})})),chrome.runtime.onMessage.addListener((async(n,e,t)=>{if(console.log("background script 接收消息",n),"getVersion"===n.action){try{const n=chrome.runtime.getManifest();n&&"string"==typeof n.version?t({version:n.version}):(console.error("Manifest 结构异常:",n),t({error:"无法获取版本信息",version:""}))}catch(i){console.error("获取版本号失败:",i),t({error:"获取版本号时发生错误",version:""})}return!0}return"tradeChange"!==n.action||(Cn({messageType:"tradeChange"}),!0)}));
