import{M as a,W as t,Z as e,Y as o,X as s,S as l,r,ag as u,N as n,O as i,Q as d,a7 as c,a8 as p,a9 as v,T as g,u as m,ah as _,n as b,ai as f,aj as x,ab as h,G as k,ak as w}from"./index-B9ANY9Zz.js";const y={class:"dog-main"},M={class:"dog-form"},j={class:"dog-form-item"},E={class:"dog-form-item-label"},I={class:"dog-form-item-content"},N={class:"dog-action"},C=a({__name:"login",setup(a){const C=t(),G=e(),{setLogin:L}=G,{auths:O}=o(G),{status:P,text:Q}=o(s()),{t:S}=l(),T=r(""),U=r(!1),W=r({status:P.value,text:Q.value}),X=()=>{window.open("https://t.me/dogeebot_bot?start=plugin_login","_blank")},Y=async()=>{if(T.value){U.value=!0;try{await L({code:T.value,platform:"sol"});U.value=!1,T.value="",await chrome.runtime.sendMessage({action:"login"}),C.replace({path:"/"})}catch(a){W.value="Network Error"==a?{status:"error",text:S("network.error")}:"timeout"==a?{status:"error",text:S("network.timeout")}:"Invalid code"==a?{status:"error",text:S("popup.login_code_error")}:{status:"error",text:a},U.value=!1}}else W.value={status:"error",text:S("popup.login_placeholder")}};return(a,t)=>{const e=f,o=k,s=u("debounce");return i(),n("div",y,[d("div",M,[d("div",j,[d("div",E,[c(g(m(S)("popup.login_label"))+" ",1),W.value.text?(i(),p(_,{key:0,text:W.value.text,status:W.value.status,"theme-color":!1},null,8,["text","status"])):v("",!0)]),d("div",I,[b(e,{class:"dog-form-item-input",value:T.value,"onUpdate:value":t[0]||(t[0]=a=>T.value=a),valueModifiers:{trim:!0},placeholder:m(S)("popup.login_placeholder"),"allow-clear":"",onPressEnter:Y},null,8,["value","placeholder"])])])]),d("div",N,[b(o,{class:"dog-action-button dog-action-get-btn",onClick:X},{default:h((()=>[c(g(m(S)("popup.login_button_get_v_code")),1)])),_:1}),x((i(),p(o,{class:"dog-action-button dog-action-verify-btn",type:"primary",disabled:!T.value},{default:h((()=>[c(g(m(S)("popup.login_button_login"))+" ",1),U.value?(i(),p(m(w),{key:0,style:{"margin-left":"6px"}})):v("",!0)])),_:1},8,["disabled"])),[[s,()=>Y(),"click"]])])])}}},[["__scopeId","data-v-6dee9087"]]);export{C as default};
