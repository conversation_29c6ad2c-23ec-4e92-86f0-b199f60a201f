import{r as e,u as o,w as t,a,t as l,g as n,m as s,i,_ as r,b as c,c as d,P as u,d as p,e as v,f as g,h as m,j as f,k as y,l as x,n as h,o as b,p as w,q as k,s as C,v as _,x as S,F as $,y as P,z as T,A as z,B as I,C as B,E as O,D as N,G as A,H as W,I as D,J as j,K as E,L as M,M as H,N as G,O as L,Q as V,R as F,S as R,T as X,U as q,V as J,W as K,X as U,Y as Q,Z as Y,$ as Z,a0 as ee,a1 as oe,a2 as te,a3 as ae,a4 as le,a5 as ne,a6 as se,a7 as ie,a8 as re,a9 as ce,aa as de,ab as ue,ac as pe,ad as ve,ae as ge,af as me}from"./index-B9ANY9Zz.js";const fe=e=>{const{componentCls:o,popoverBg:t,popoverColor:a,width:l,fontWeightStrong:n,popoverPadding:s,boxShadowSecondary:i,colorTextHeading:u,borderRadiusLG:p,zIndexPopup:v,marginXS:g,colorBgElevated:m}=e;return[{[o]:r(r({},d(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:v,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--antd-arrow-background-color":m,"&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:t,backgroundClip:"padding-box",borderRadius:p,boxShadow:i,padding:s},[`${o}-title`]:{minWidth:l,marginBottom:g,color:u,fontWeight:n},[`${o}-inner-content`]:{color:a}})},c(e,{colorBg:"var(--antd-arrow-background-color)"}),{[`${o}-pure`]:{position:"relative",maxWidth:"none",[`${o}-content`]:{display:"inline-block"}}}]},ye=e=>{const{componentCls:o}=e;return{[o]:u.map((t=>{const a=e[`${t}-6`];return{[`&${o}-${t}`]:{"--antd-arrow-background-color":a,[`${o}-inner`]:{backgroundColor:a},[`${o}-arrow`]:{background:"transparent"}}}}))}},xe=e=>{const{componentCls:o,lineWidth:t,lineType:a,colorSplit:l,paddingSM:n,controlHeight:s,fontSize:i,lineHeight:r,padding:c}=e,d=s-Math.round(i*r),u=d/2,p=d/2-t,v=c;return{[o]:{[`${o}-inner`]:{padding:0},[`${o}-title`]:{margin:0,padding:`${u}px ${v}px ${p}px`,borderBottom:`${t}px ${a} ${l}`},[`${o}-inner-content`]:{padding:`${n}px ${v}px`}}}},he=n("Popover",(e=>{const{colorBgElevated:o,colorText:t,wireframe:a}=e,l=s(e,{popoverBg:o,popoverColor:t,popoverPadding:12});return[fe(l),ye(l),a&&xe(l),i(l,"zoom-big")]}),(e=>{let{zIndexPopupBase:o}=e;return{zIndexPopup:o+30,width:177}})),be=p(v({compatConfig:{MODE:3},name:"APopover",inheritAttrs:!1,props:g(r(r({},P()),{content:T(),title:T()}),r(r({},_()),{trigger:"hover",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1})),setup(o,t){let{expose:a,slots:l,attrs:n}=t;const s=e();m(void 0===o.visible),a({getPopupDomNode:()=>{var e,o;return null===(o=null===(e=s.value)||void 0===e?void 0:e.getPopupDomNode)||void 0===o?void 0:o.call(e)}});const{prefixCls:i,configProvider:r}=f("popover",o),[c,d]=he(i),u=y((()=>r.getPrefixCls())),p=()=>{var e,t;const{title:a=S(null===(e=l.title)||void 0===e?void 0:e.call(l)),content:n=S(null===(t=l.content)||void 0===t?void 0:t.call(l))}=o,s=!!(Array.isArray(a)?a.length:a),r=!!(Array.isArray(n)?n.length:a);return s||r?h($,null,[s&&h("div",{class:`${i.value}-title`},[a]),h("div",{class:`${i.value}-inner-content`},[n])]):null};return()=>{const e=x(o.overlayClassName,d.value);return c(h(C,b(b(b({},k(o,["title","content"])),n),{},{prefixCls:i.value,ref:s,overlayClassName:e,transitionName:w(u.value,"zoom-big",o.transitionName),"data-popover-inject":!0}),{title:p,default:l.default}))}}})),we=n("Popconfirm",(e=>(e=>{const{componentCls:o,iconCls:t,zIndexPopup:a,colorText:l,colorWarning:n,marginXS:s,fontSize:i,fontWeightStrong:r,lineHeight:c}=e;return{[o]:{zIndex:a,[`${o}-inner-content`]:{color:l},[`${o}-message`]:{position:"relative",marginBottom:s,color:l,fontSize:i,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${o}-message-icon ${t}`]:{color:n,fontSize:i,flex:"none",lineHeight:1,paddingTop:(Math.round(i*c)-i)/2},"&-title":{flex:"auto",marginInlineStart:s},"&-title-only":{fontWeight:r}},[`${o}-description`]:{position:"relative",marginInlineStart:i+s,marginBottom:s,color:l,fontSize:i},[`${o}-buttons`]:{textAlign:"end",button:{marginInlineStart:s}}}}})(e)),(e=>{const{zIndexPopupBase:o}=e;return{zIndexPopup:o+60}}));const ke=p(v({compatConfig:{MODE:3},name:"APopconfirm",inheritAttrs:!1,props:g(r(r({},P()),{prefixCls:String,content:T(),title:T(),description:T(),okType:j("primary"),disabled:{type:Boolean,default:!1},okText:T(),cancelText:T(),icon:T(),okButtonProps:D(),cancelButtonProps:D(),showCancel:{type:Boolean,default:!0},onConfirm:Function,onCancel:Function}),r(r({},_()),{trigger:"click",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0,okType:"primary",disabled:!1})),slots:Object,setup(n,s){let{slots:i,emit:c,expose:d,attrs:u}=s;const p=e();m(void 0===n.visible),d({getPopupDomNode:()=>{var e,o;return null===(o=null===(e=p.value)||void 0===e?void 0:e.getPopupDomNode)||void 0===o?void 0:o.call(e)}});const[v,g]=function(n,s){const{defaultValue:i,value:r=e()}=s||{};let c=n;void 0!==r.value&&(c=o(r)),void 0!==i&&(c="function"==typeof i?i():i);const d=e(c),u=e(c);return t((()=>{let e=void 0!==r.value?r.value:d.value;s.postState&&(e=s.postState(e)),u.value=e})),a(r,(()=>{d.value=r.value})),[u,function(e){const o=u.value;d.value=e,l(u.value)!==e&&s.onChange&&s.onChange(e,o)}]}(!1,{value:z(n,"open")}),C=(e,o)=>{void 0===n.open&&g(e),c("update:open",e),c("openChange",e,o)},_=e=>{C(!1,e)},S=e=>{var o;return null===(o=n.onConfirm)||void 0===o?void 0:o.call(n,e)},$=e=>{var o;C(!1,e),null===(o=n.onCancel)||void 0===o||o.call(n,e)},P=e=>{const{disabled:o}=n;o||C(e)},{prefixCls:T,getPrefixCls:D}=f("popconfirm",n),j=y((()=>D())),H=y((()=>D("btn"))),[G]=we(T),[L]=I("Popconfirm",E.Popconfirm),V=()=>{var e,o,t,a,l;const{okButtonProps:s,cancelButtonProps:c,title:d=(null===(e=i.title)||void 0===e?void 0:e.call(i)),description:u=(null===(o=i.description)||void 0===o?void 0:o.call(i)),cancelText:p=(null===(t=i.cancel)||void 0===t?void 0:t.call(i)),okText:v=(null===(a=i.okText)||void 0===a?void 0:a.call(i)),okType:g,icon:m=(null===(l=i.icon)||void 0===l?void 0:l.call(i))||h(O,null,null),showCancel:f=!0}=n,{cancelButton:y,okButton:x}=i,b=r({onClick:$,size:"small"},c),w=r(r(r({onClick:S},N(g)),{size:"small"}),s);return h("div",{class:`${T.value}-inner-content`},[h("div",{class:`${T.value}-message`},[m&&h("span",{class:`${T.value}-message-icon`},[m]),h("div",{class:[`${T.value}-message-title`,{[`${T.value}-message-title-only`]:!!u}]},[d])]),u&&h("div",{class:`${T.value}-description`},[u]),h("div",{class:`${T.value}-buttons`},[f?y?y(b):h(A,b,{default:()=>[p||L.value.cancelText]}):null,x?x(w):h(W,{buttonProps:r(r({size:"small"},N(g)),s),actionFn:S,close:_,prefixCls:H.value,quitOnNullishReturnValue:!0,emitEvent:!0},{default:()=>[v||L.value.okText]})])])};return()=>{var e;const{placement:o,overlayClassName:t,trigger:a="click"}=n,l=function(e,o){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&o.indexOf(a)<0&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(a=Object.getOwnPropertySymbols(e);l<a.length;l++)o.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(t[a[l]]=e[a[l]])}return t}(n,["placement","overlayClassName","trigger"]),s=k(l,["title","content","cancelText","okText","onUpdate:open","onConfirm","onCancel","prefixCls"]),r=x(T.value,t);return G(h(be,b(b(b({},s),u),{},{trigger:a,placement:o,onOpenChange:P,open:v.value,overlayClassName:r,transitionName:w(j.value,"zoom-big",n.transitionName),ref:p,"data-popover-inject":!0}),{default:()=>[B((null===(e=i.default)||void 0===e?void 0:e.call(i))||[],{onKeydown:e=>{(e=>{e.keyCode===M.ESC&&v&&C(!1,e)})(e)}},!1)],content:V}))}}})),Ce=H({__name:"index",props:{size:{type:Number,default:16},color:{type:String,default:"#fff"},speed:{type:Number,default:.8}},setup(e){const o=e,t=y((()=>({animationDuration:`${o.speed}s`,color:o.color,fontSize:`${o.size}px`})));return(o,a)=>(L(),G("div",{class:"dog-loading-container",style:F({width:e.size+"px",height:e.size+"px",color:e.color})},[V("i",{class:"dog-iconfont dog-icon-loading dog-loading-icon",style:F(t.value)},null,4)],4))}},[["__scopeId","data-v-ee18e591"]]),_e=[{label:"Axiom",url:"https://axiom.trade/",host:"axiom.trade",popup:!0},{label:"Photon",url:"https://photon-sol.tinyastro.io/",host:"photon-sol.tinyastro.io",popup:!0},{label:"Gmgn",url:"https://gmgn.ai/",host:"gmgn.ai",popup:!0},{label:"Xxyy",url:"https://www.xxyy.io/",host:"www.xxyy.io",popup:!1}],Se={class:"dog-external-website"},$e={class:"dog-external-website-title"},Pe={class:"dog-external-website-content"},Te=["onClick"],ze=H({__name:"external-website",props:{disabled:{type:Boolean,default:!1}},setup(e){const{t:t}=R(),a=e,l=y((()=>_e.filter((e=>e.popup))));return(e,n)=>(L(),G("div",Se,[V("div",$e,X(o(t)("popup.home_supported_website_label")),1),V("div",Pe,[(L(!0),G($,null,q(l.value,((e,o)=>(L(),G("div",{class:J(["dog-external-website-item",{"dog-external-website-item-disabled":a.disabled}]),key:"website"+o,onClick:o=>{return t=e.url,void(a.disabled||window.open(t,"_blank"));var t}},[n[0]||(n[0]=V("i",{class:"dog-iconfont dog-icon-a-icon_shijiediqiu dog-external-website-icon"},null,-1)),V("span",null,X(e.label),1)],10,Te)))),128))])]))}},[["__scopeId","data-v-5f27aa77"]]);const Ie={class:"dog-main"},Be={class:"dog-default-wallet"},Oe={key:0,class:"dog-network-error-wrapper"},Ne={class:"dog-network-error"},Ae={class:"dog-default-wallet-address"},We={class:"dog-default-wallet-value"},De={class:"dog-footer"},je={class:"dog-version"},Ee={class:"dog-footer-action"},Me=H({__name:"home",setup(a){const l=K(),{t:n}=R(),{subString:s}=de(),i=U(),{status:r,text:c,date:d}=Q(i),{setMessageStatus:u,clearMessageStatus:p}=i,v=Y(),{logOut:g,setTgId:m}=v,{tgId:f}=Q(v),y=Z(),{platform:x,hide:b}=Q(y),{setHide:w}=y,k=ee(),{upsertWallet:C,getWalletByPlatform:_}=k,S=oe(),{upsertSettings:P}=S,T=te(),{upsertShortcutKey:z}=T,I=ae(),{upsertPresetsWalletToken:B}=I,O=le(),{upsertPresetsWallet:N}=O,A=e(!1),W=async()=>{await g(x.value)&&(u({status:"success",text:n("popup.home_plugin_exited_message")}),await chrome.runtime.sendMessage({action:"logou"}),l.replace({path:"/login"}))},D=()=>{w(!1)},j=e(!1),E=async()=>{j.value=!0;const e=await pe(),{address_list:o,tgid:t}=e;C({tgid:t,data:o,platform:x.value}),m(t),(async()=>{{const e=await chrome.storage.local.get(null);if(e.dog_settings){const o=await me(),{gas:t,saveGas:a}=o,l=e.dog_settings;await P({tgid:f.value,data:{buy:{...l.buy,gas:t,saveGas:a},sell:{...l.sell,gas:t,saveGas:a}},platform:x.value}),await chrome.storage.local.remove("dog_settings"),localStorage.removeItem("dog_settings")}if(e.dog_shortcutkey){const o=e.dog_shortcutkey;z({platform:x.value,data:{buy:o.buy,sell:o.sell},tgid:f.value}),await chrome.storage.local.remove("dog_shortcutkey"),localStorage.removeItem("dog_shortcutkey")}if(e.dog_sellTokenSelections){const o=e.dog_sellTokenSelections;B({tgid:f.value,data:o,platform:x.value}),await chrome.storage.local.remove("dog_sellTokenSelections"),localStorage.removeItem("dog_sellTokenSelections")}if(e.dog_user){const o=JSON.parse(JSON.parse(e.dog_user));(null==o?void 0:o.walletSelections)&&await N({tgid:f.value,data:o.walletSelections,platform:x.value}),await chrome.storage.local.remove("dog_user"),localStorage.removeItem("dog_user")}}})(),j.value=!1},M=async()=>{A.value=!0,p(),await E(),ge((()=>{A.value=!1}))},H=e({});t((async()=>{try{const e=await _({platform:x.value,tgid:f.value});e&&e.data?H.value=e.data.find((e=>e.is_new))||{}:H.value={}}catch(e){H.value={}}})),ne((async()=>{E()})),se((async()=>{E()}));const F=e("");(async()=>{const e=await ve.getVersion();F.value=e})();const q=e(!1),J=function(o,t=1e3){const a=e(0);let l=null;return()=>{a.value++,l&&clearTimeout(l),l=setTimeout((()=>{a.value=0}),t),3===a.value&&(o(),a.value=0)}}((()=>{q.value=!0}),500),fe=async()=>{q.value=!1,await chrome.storage.local.clear(),localStorage.clear(),window.location.reload()};return(e,t)=>{var a,l,i,d,u;const p=ke;return L(),G("div",Ie,[V("div",Be,["network-error"==o(r)?(L(),G("div",Oe,[V("div",Ne,[t[2]||(t[2]=V("i",{class:"dog-iconfont dog-icon-cuowu dog-network-error-icon"},null,-1)),ie(" "+X(o(c)),1)]),V("div",{class:"dog-network-error-tryAgain",onClick:M},X(o(n)("common.retry")),1)])):(L(),G($,{key:1},[A.value?(L(),re(Ce,{key:0})):ce("",!0),!A.value&&(null==(a=H.value)?void 0:a.address)?(L(),G($,{key:1},[V("div",Ae,X(null==(l=H.value)?void 0:l.wallet)+": "+X(o(s)(null==(i=H.value)?void 0:i.address,4,4)),1),V("div",We,[V("span",null,X(null==(d=H.value)?void 0:d.balance)+" SOL",1),V("span",null,"（$"+X(null==(u=H.value)?void 0:u.usd_price)+")",1)])],64)):ce("",!0)],64))]),h(ze,{disabled:j.value},null,8,["disabled"]),V("div",De,[V("div",je,[h(p,{placement:"topLeft","ok-text":o(n)("common.confirm"),"cancel-text":o(n)("common.cancel"),disabled:"",open:q.value,onCancel:t[1]||(t[1]=()=>q.value=!1),onConfirm:fe},{title:ue((()=>[ie(X(o(n)("popup.home_reset_cache")),1)])),default:ue((()=>[F.value?(L(),G("span",{key:0,onClick:t[0]||(t[0]=(...e)=>o(J)&&o(J)(...e))},"Extension V."+X(F.value),1)):ce("",!0)])),_:1},8,["ok-text","cancel-text","open"])]),V("div",Ee,[o(b)?(L(),G("div",{key:0,class:"dog-footer-action-item-panel",onClick:D},[t[3]||(t[3]=V("i",{class:"dog-iconfont dog-icon-xianshikai dog-footer-action-icon"},null,-1)),ie(" "+X(o(n)("popup.home_display_panel")),1)])):ce("",!0),V("div",{class:"dog-footer-action-item",onClick:W},[t[4]||(t[4]=V("i",{class:"dog-iconfont dog-icon-tuichu dog-footer-action-icon"},null,-1)),ie(" "+X(o(n)("popup.home_log_out")),1)])])])])}}},[["__scopeId","data-v-cf76ca1d"]]);export{Me as default};
