{"name": "__MSG_appName__", "version": "1.2.8", "description": "__MSG_appDescription__", "default_locale": "en", "manifest_version": 3, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxP/EogsLn3DkSyYjTfEBnU68QPt8NmJdjM96rqtjds9qPn05F7/irfDdYyPlsPFpkNQA1OyUIvy+avbbrJHa0lbIWfMgYYOEvI+nwtzPe9auL6ykcnCVbTZF109gdbRXJatnKJVFZR0qo4cckEJXrehMXZgSWcUK2YXFI/39bJ6af6u9mpwsb5MSE2F36OgZw4C3D51ABLgGJPBd0OZsGKCSOF7hfk8w7e4pWK6xsOQHx+vvdyl6CD+LCBDwDG2p2Q8cw3KSNcUB4mdD0X/1MQGtehu+CjM3Mn9qtSKIkKou4GRma6cxVJEZSXUe1tSzRMA9ibmHlKnbexiyv5SwlQIDAQAB", "content_security_policy": {"extension_pages": "default-src 'self'; font-src 'self' data:; script-src 'self'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://plugin-api.dogee.io;", "sandbox": "sandbox allow-scripts; script-src 'self'"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://gmgn.ai/sol/token/*", "https://photon-sol.tinyastro.io/*/lp/*", "https://axiom.trade/meme/*", "https://xxyy.io/sol/*", "https://www.xxyy.io/sol/*"], "exclude_matches": ["*://*/*chrome-extension*/*"], "css": ["content.css"], "js": ["content.js"], "run_at": "document_idle"}], "permissions": ["browsingData", "scripting", "storage", "webNavigation"], "host_permissions": ["https://ape.pro/*", "https://ave.ai/*", "https://axiom.trade/*", "https://backup.axiom.trade/*", "https://debot.ai/*", "https://dexscreener.com/*", "https://discord.com/*", "https://extension.bloombot.app/*", "https://gmgn.ai/*", "https://neo-backup.bullx.io/*", "https://neo.bullx.io/*", "https://photon-sol.tinyastro.io/*", "https://pump.fun/*", "https://solscan.io/*", "https://trade.padre.gg/*", "https://web.telegram.org/*", "https://x.com/*", "https://web3.okx.com/*", "https://xxyy.io/*", "https://www.xxyy.io/*"], "web_accessible_resources": [{"resources": ["/img/app.png", "assets/styles/*", "icons/*.svg"], "matches": ["https://gmgn.ai/*", "https://photon-sol.tinyastro.io/*", "https://axiom.trade/*", "https://xxyy.io/*", "https://www.xxyy.io/*"]}, {"resources": ["insert.js"], "matches": ["https://gmgn.ai/*", "https://photon-sol.tinyastro.io/*", "https://axiom.trade/*", "https://xxyy.io/*", "https://www.xxyy.io/*"], "extension_ids": []}], "action": {"default_popup": "index.html", "default_icon": {"16": "/img/app.png", "32": "/img/app.png", "48": "/img/app.png", "128": "/img/app.png"}, "default_title": "<PERSON><PERSON>"}, "icons": {"16": "/img/app.png", "32": "/img/app.png", "48": "/img/app.png", "128": "/img/app.png"}}